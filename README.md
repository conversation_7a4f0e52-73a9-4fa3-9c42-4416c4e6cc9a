# API de Geração de PDFs - Relatórios Detalhados de Gastos

Esta API Node.js foi desenvolvida para gerar PDFs de relatórios detalhados de gastos baseados no modelo fornecido pelo Estado de Santa Catarina.

## 🚀 Características

- **Geração de PDF**: Cria PDFs formatados seguindo o modelo oficial
- **Suporte a arquivos grandes**: Configurado para processar JSONs de até 50MB
- **Hierarquia visual**: Implementa indentação baseada no nível hierárquico dos dados
- **Formatação de moeda**: Valores formatados em Real brasileiro (R$)
- **Cabeçalho personalizado**: Logo do Estado de SC e filtros aplicados
- **Paginação automática**: Quebra de página quando necessário
- **Validação robusta**: Validação completa dos dados de entrada
- **Rate limiting**: Proteção contra abuso da API
- **Logs detalhados**: Monitoramento de performance e uso

## 📁 Estrutura do Projeto

```
src/
├── config/          # Configurações da aplicação
├── controllers/     # Controladores das rotas
├── middleware/      # Middlewares personalizados
├── services/        # Lógica de negócio
└── utils/          # Utilitários e helpers
```

## 🛠️ Instalação e Configuração

### Pré-requisitos
- Node.js 14+ 
- npm ou yarn

### Instalação
```bash
# Clone o repositório
git clone <repository-url>
cd pdf-generator-api

# Instale as dependências
npm install

# Execute em modo de desenvolvimento
npm run dev

# Execute em produção
npm start
```

### Configuração
Crie um arquivo `.env` na raiz do projeto (opcional):
```env
PORT=3000
NODE_ENV=development
```

## 📡 Endpoints da API

### 1. Gerar PDF
**POST** `/api/pdf/generate`

Gera um PDF baseado nos dados JSON fornecidos.

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "logo-sc": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "filtros": {
    "Entidade Custos": "4. SEF - SECRETARIA DE ESTADO DA FAZENDA",
    "Período": "2020-01 a 2020-01",
    "Tipo de Gasto": "Todos"
  },
  "dadosRelatorio": [
    {
      "nivel": 0,
      "nome": "Total",
      "valor_custo": 31587564.42,
      "valor_despesa": 37932994.54,
      "valor_investimento": 1707430.44,
      "totalfinanceiro": 71227989.40
    }
  ],
  "dadosEmissao": {
    "Sistema": "Sistema XYZ",
    "Usuário": "João Silva",
    "Versão": "1.0.0"
  }
}
```

**Response:**
- **200 OK**: Retorna o PDF como attachment
- **400 Bad Request**: Dados inválidos
- **413 Payload Too Large**: Arquivo muito grande
- **429 Too Many Requests**: Rate limit excedido
- **500 Internal Server Error**: Erro interno

### 2. Validar Dados
**POST** `/api/pdf/validate`

Valida os dados JSON sem gerar o PDF.

**Response:**
```json
{
  "isValid": true,
  "errors": [],
  "recordCount": 150,
  "timestamp": "08/07/2025 14:30"
}
```

### 3. Informações da API
**GET** `/api/pdf/info`

Retorna informações sobre a API e seus endpoints.

### 4. Teste com Dados Mock
**GET** `/api/pdf/test`

Gera um PDF usando os dados do arquivo `dados-json.json` como exemplo.

### 5. Health Check
**GET** `/health`

Verifica se a API está funcionando.

## 📊 Estrutura dos Dados

### Campos Obrigatórios

#### `logo-sc` (string)
Imagem do logo em formato base64. Deve incluir o prefixo `data:image/...;base64,`.

#### `filtros` (object)
Filtros aplicados na geração do relatório:
- `"Entidade Custos"`: Nome da entidade
- `"Período"`: Período dos dados
- `"Tipo de Gasto"`: Tipo de gasto selecionado

#### `dadosRelatorio` (array)
Array com os dados da tabela principal. Cada item deve conter:
- `nivel` (number): Nível hierárquico (0, 1, 2, 3, 4...)
- `nome` (string): Nome/descrição do item
- `valor_custo` (number): Valor de custo
- `valor_despesa` (number): Valor de despesa  
- `valor_investimento` (number): Valor de investimento
- `totalfinanceiro` (number): Total financeiro
- `sigla` (string, opcional): Sigla da entidade
- `codigo` (number, opcional): Código identificador

#### `dadosEmissao` (object)
Dados exibidos no rodapé de cada página.

## 🎨 Formatação Visual

### Layout da Página
- **Orientação**: Paisagem (Landscape) para melhor aproveitamento do espaço horizontal
- **Formato**: A4 (842 x 595 pixels em paisagem)
- **Larguras das colunas**: Otimizadas para paisagem
  - Detalhamento: 350px (permite quebra de linha e indentação)
  - Custo, Despesa, Investimento, Total: 98px cada

### Hierarquia
A indentação é calculada automaticamente baseada no campo `nivel`:
- Nível 0: Sem indentação
- Nível 1: 25px de indentação
- Nível 2: 40px de indentação
- E assim por diante...

### Formatação de Moeda
Todos os valores monetários são formatados automaticamente para o padrão brasileiro:
- `1234.56` → `R$ 1.234,56`
- Fonte: 9pt para boa legibilidade
- Alinhamento: À direita nas colunas

### Paginação
- **Orientação paisagem** para máximo aproveitamento horizontal
- Cabeçalho em todas as páginas
- Filtros apenas na primeira página
- Rodapé em todas as páginas
- Quebra automática quando necessário

## 🔧 Configurações Avançadas

### Limites
- **Tamanho máximo do JSON**: 50MB
- **Rate limit**: 10 requisições por minuto por IP
- **Timeout**: 2 minutos por requisição

### Personalização
Edite `src/config/app.js` para ajustar:
- Orientação da página (landscape/portrait)
- Tamanhos de página
- Margens
- Fontes
- Limites de upload

## 🚨 Tratamento de Erros

A API retorna erros estruturados:

```json
{
  "error": "Tipo do erro",
  "message": "Descrição detalhada",
  "details": ["Lista de erros específicos"]
}
```

### Códigos de Status
- **400**: Dados inválidos ou malformados
- **413**: Payload muito grande
- **429**: Rate limit excedido
- **500**: Erro interno do servidor

## 📝 Exemplo de Uso

### cURL
```bash
curl -X POST http://localhost:3000/api/pdf/generate \
  -H "Content-Type: application/json" \
  -d @dados-json.json \
  --output relatorio.pdf
```

### JavaScript (fetch)
```javascript
const response = await fetch('http://localhost:3000/api/pdf/generate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(dadosJSON)
});

if (response.ok) {
  const blob = await response.blob();
  // Processar o PDF...
}
```

## 🔍 Monitoramento

### Logs
A API gera logs detalhados incluindo:
- Tempo de processamento
- Tamanho do PDF gerado
- Número de registros processados
- Informações de requisição

### Métricas
Headers de resposta incluem:
- `X-Processing-Time`: Tempo de processamento
- `X-Records-Processed`: Número de registros processados

## 🤝 Contribuição

1. Faça um fork do projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT.
