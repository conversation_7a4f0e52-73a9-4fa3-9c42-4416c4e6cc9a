/**
 * Exemplo de uso da API de Geração de PDFs
 * 
 * Este arquivo demonstra como usar a API para gerar PDFs
 * de relatórios detalhados de gastos.
 */

const fs = require('fs');
const path = require('path');
const fetch = require('node-fetch');

// Configuração da API
const API_BASE_URL = 'http://localhost:3000';

/**
 * Exemplo 1: Gerar PDF usando dados do arquivo JSON
 */
async function exemploComArquivoJSON() {
  try {
    console.log('🔄 Carregando dados do arquivo JSON...');
    
    // Carrega os dados do arquivo
    const dadosPath = path.join(__dirname, 'dados-json.json');
    const dados = JSON.parse(fs.readFileSync(dadosPath, 'utf8'));
    
    // Adiciona dados de emissão se não existirem
    if (!dados.dadosEmissao) {
      dados.dadosEmissao = {
        'Sistema': 'Sistema de Exemplo',
        'Usuário': '<PERSON>u<PERSON><PERSON> Teste',
        'Versão': '1.0.0'
      };
    }

    console.log(`📊 Processando ${dados.dadosRelatorio.length} registros...`);

    // Faz a requisição para a API
    const response = await fetch(`${API_BASE_URL}/api/pdf/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(dados)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Erro da API: ${error.message}`);
    }

    // Salva o PDF
    const pdfBuffer = await response.arrayBuffer();
    const fileName = `relatorio-exemplo-${Date.now()}.pdf`;
    fs.writeFileSync(fileName, Buffer.from(pdfBuffer));

    console.log(`✅ PDF gerado com sucesso: ${fileName}`);
    console.log(`📄 Tamanho: ${(pdfBuffer.byteLength / 1024).toFixed(2)} KB`);

  } catch (error) {
    console.error('❌ Erro:', error.message);
  }
}

/**
 * Exemplo 2: Validar dados antes de gerar PDF
 */
async function exemploValidacao() {
  try {
    console.log('🔍 Validando dados...');
    
    const dadosPath = path.join(__dirname, 'dados-json.json');
    const dados = JSON.parse(fs.readFileSync(dadosPath, 'utf8'));

    const response = await fetch(`${API_BASE_URL}/api/pdf/validate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(dados)
    });

    const resultado = await response.json();

    if (resultado.isValid) {
      console.log('✅ Dados válidos!');
      console.log(`📊 Registros: ${resultado.recordCount}`);
    } else {
      console.log('❌ Dados inválidos:');
      resultado.errors.forEach(error => console.log(`  - ${error}`));
    }

  } catch (error) {
    console.error('❌ Erro:', error.message);
  }
}

/**
 * Exemplo 3: Criar dados personalizados
 */
async function exemploPersonalizado() {
  try {
    console.log('🎨 Criando dados personalizados...');

    // Usa o logo do arquivo JSON original ou cria um placeholder
    const dadosPath = path.join(__dirname, 'dados-json.json');
    const dadosOriginais = JSON.parse(fs.readFileSync(dadosPath, 'utf8'));
    const logoBase64 = dadosOriginais['logo-sc'] || 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA60e6kgAAAABJRU5ErkJggg==';

    const dadosPersonalizados = {
      'logo-sc': logoBase64,
      'filtros': {
        'Entidade Custos': 'Exemplo - Secretaria de Teste',
        'Período': '2025-01 a 2025-01',
        'Tipo de Gasto': 'Todos os tipos'
      },
      'dadosRelatorio': [
        {
          nivel: 0,
          nome: 'Total Geral',
          valor_custo: 100000.00,
          valor_despesa: 50000.00,
          valor_investimento: 25000.00,
          totalfinanceiro: 175000.00,
          sigla: '',
          codigo: 0
        },
        {
          nivel: 1,
          nome: 'Categoria A',
          valor_custo: 60000.00,
          valor_despesa: 30000.00,
          valor_investimento: 15000.00,
          totalfinanceiro: 105000.00,
          sigla: 'CAT-A',
          codigo: 1001
        },
        {
          nivel: 2,
          nome: 'Subcategoria A1',
          valor_custo: 35000.00,
          valor_despesa: 18000.00,
          valor_investimento: 9000.00,
          totalfinanceiro: 62000.00,
          sigla: 'SUB-A1',
          codigo: 1001001
        },
        {
          nivel: 2,
          nome: 'Subcategoria A2',
          valor_custo: 25000.00,
          valor_despesa: 12000.00,
          valor_investimento: 6000.00,
          totalfinanceiro: 43000.00,
          sigla: 'SUB-A2',
          codigo: 1001002
        },
        {
          nivel: 1,
          nome: 'Categoria B',
          valor_custo: 40000.00,
          valor_despesa: 20000.00,
          valor_investimento: 10000.00,
          totalfinanceiro: 70000.00,
          sigla: 'CAT-B',
          codigo: 1002
        }
      ],
      'dadosEmissao': {
        'Sistema': 'Sistema Personalizado',
        'Usuário': 'Desenvolvedor',
        'Versão': '2.0.0',
        'Ambiente': 'Teste'
      }
    };

    const response = await fetch(`${API_BASE_URL}/api/pdf/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(dadosPersonalizados)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Erro da API: ${error.message}`);
    }

    const pdfBuffer = await response.arrayBuffer();
    const fileName = `relatorio-personalizado-${Date.now()}.pdf`;
    fs.writeFileSync(fileName, Buffer.from(pdfBuffer));

    console.log(`✅ PDF personalizado gerado: ${fileName}`);

  } catch (error) {
    console.error('❌ Erro:', error.message);
  }
}

/**
 * Exemplo 4: Verificar informações da API
 */
async function exemploInfoAPI() {
  try {
    console.log('ℹ️ Obtendo informações da API...');

    const response = await fetch(`${API_BASE_URL}/api/pdf/info`);
    const info = await response.json();

    console.log('📋 Informações da API:');
    console.log(`  Nome: ${info.name}`);
    console.log(`  Versão: ${info.version}`);
    console.log(`  Descrição: ${info.description}`);
    console.log('  Endpoints disponíveis:');
    
    Object.entries(info.endpoints).forEach(([name, endpoint]) => {
      console.log(`    ${endpoint.method} ${endpoint.path} - ${endpoint.description}`);
    });

  } catch (error) {
    console.error('❌ Erro:', error.message);
  }
}

/**
 * Exemplo 5: Testar com dados mock da API
 */
async function exemploTesteMock() {
  try {
    console.log('🧪 Testando com dados mock...');

    const response = await fetch(`${API_BASE_URL}/api/pdf/test`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Erro da API: ${error.message}`);
    }

    const pdfBuffer = await response.arrayBuffer();
    const fileName = `relatorio-mock-${Date.now()}.pdf`;
    fs.writeFileSync(fileName, Buffer.from(pdfBuffer));

    console.log(`✅ PDF de teste gerado: ${fileName}`);

  } catch (error) {
    console.error('❌ Erro:', error.message);
  }
}

// Função principal para executar os exemplos
async function main() {
  console.log('🚀 Exemplos de uso da API de Geração de PDFs\n');

  // Verifica se a API está rodando
  try {
    const healthResponse = await fetch(`${API_BASE_URL}/health`);
    if (!healthResponse.ok) {
      throw new Error('API não está respondendo');
    }
    console.log('✅ API está rodando\n');
  } catch (error) {
    console.error('❌ Erro: API não está rodando. Execute "npm run dev" primeiro.\n');
    return;
  }

  // Executa os exemplos
  await exemploInfoAPI();
  console.log('\n' + '='.repeat(50) + '\n');
  
  await exemploValidacao();
  console.log('\n' + '='.repeat(50) + '\n');
  
  await exemploComArquivoJSON();
  console.log('\n' + '='.repeat(50) + '\n');
  
  await exemploPersonalizado();
  console.log('\n' + '='.repeat(50) + '\n');
  
  await exemploTesteMock();
  
  console.log('\n🎉 Todos os exemplos foram executados!');
}

// Executa se for chamado diretamente
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  exemploComArquivoJSON,
  exemploValidacao,
  exemploPersonalizado,
  exemploInfoAPI,
  exemploTesteMock
};
