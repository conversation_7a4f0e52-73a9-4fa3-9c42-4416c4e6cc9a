require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');

const config = require('./config/app');
const pdfRoutes = require('./controllers/pdfController');
const errorHandler = require('./middleware/errorHandler');

const app = express();

// Middlewares de segurança e otimização
app.use(helmet());
app.use(compression());
app.use(cors());
app.use(morgan('combined'));

// Configuração para JSON com limite aumentado para arquivos grandes
app.use(express.json({ 
  limit: config.upload.maxFileSize,
  extended: true 
}));

app.use(express.urlencoded({ 
  limit: config.upload.maxFileSize,
  extended: true 
}));

// Rotas
app.use('/api/pdf', pdfRoutes);

// Rota de health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    version: require('../package.json').version
  });
});

// Middleware de tratamento de erros
app.use(errorHandler);

// Middleware para rotas não encontradas
app.use((req, res) => {
  res.status(404).json({
    error: 'Rota não encontrada',
    message: `A rota ${req.method} ${req.originalUrl} não existe`
  });
});

const PORT = config.port;

app.listen(PORT, () => {
  console.log(`🚀 Servidor rodando na porta ${PORT}`);
  console.log(`📊 Ambiente: ${config.env}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
  console.log(`📄 Endpoint PDF: http://localhost:${PORT}/api/pdf/generate`);
});

module.exports = app;
