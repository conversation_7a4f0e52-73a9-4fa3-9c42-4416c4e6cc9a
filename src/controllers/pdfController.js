const express = require('express');
const router = express.Router();
const PDFService = require('../services/pdfService');
const { validateInputData, createValidationError } = require('../utils/validator');
const { formatDateTime } = require('../utils/formatters');
const {
  validatePDFRequest,
  logRequest,
  rateLimiter,
  validatePayloadSize
} = require('../middleware/requestValidator');

/**
 * Endpoint para geração de PDF
 * POST /api/pdf/generate
 */
router.post('/generate',
  rateLimiter,
  validatePayloadSize,
  logRequest,
  validatePDFRequest,
  async (req, res, next) => {
  try {
    const startTime = Date.now();

    // Os dados já foram validados pelo middleware validatePDFRequest

    // Instancia o serviço de PDF
    const pdfService = new PDFService();
    
    // Gera o PDF
    const pdfBuffer = await pdfService.generatePDF(req.body);
    
    const endTime = Date.now();
    const processingTime = endTime - startTime;

    // Log da operação
    console.log(`PDF gerado com sucesso em ${processingTime}ms`);
    console.log(`Tamanho do PDF: ${(pdfBuffer.length / 1024).toFixed(2)} KB`);
    console.log(`Registros processados: ${req.body.dadosRelatorio?.length || 0}`);

    // Gera nome do arquivo baseado na data atual
    const now = new Date();
    const fileName = `relatorio-detalhado-gasto-${now.toISOString().slice(0, 10).replace(/-/g, '-')}_${now.toTimeString().slice(0, 5).replace(':', '-')}.pdf`;

    // Configura headers para download
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="${fileName}"`,
      'Content-Length': pdfBuffer.length,
      'X-Processing-Time': `${processingTime}ms`,
      'X-Records-Processed': req.body.dadosRelatorio?.length || 0
    });

    // Envia o PDF
    res.send(pdfBuffer);

  } catch (error) {
    next(error);
  }
});

/**
 * Endpoint para validação de dados sem gerar PDF
 * POST /api/pdf/validate
 */
router.post('/validate', (req, res, next) => {
  try {
    const validation = validateInputData(req.body);
    
    res.json({
      isValid: validation.isValid,
      errors: validation.errors,
      recordCount: req.body.dadosRelatorio?.length || 0,
      timestamp: formatDateTime(new Date())
    });

  } catch (error) {
    next(error);
  }
});

/**
 * Endpoint para informações da API
 * GET /api/pdf/info
 */
router.get('/info', (req, res) => {
  const packageInfo = require('../../package.json');
  
  res.json({
    name: packageInfo.name,
    version: packageInfo.version,
    description: packageInfo.description,
    endpoints: {
      generate: {
        method: 'POST',
        path: '/api/pdf/generate',
        description: 'Gera PDF a partir de dados JSON',
        contentType: 'application/json'
      },
      validate: {
        method: 'POST',
        path: '/api/pdf/validate',
        description: 'Valida dados JSON sem gerar PDF',
        contentType: 'application/json'
      },
      info: {
        method: 'GET',
        path: '/api/pdf/info',
        description: 'Informações sobre a API'
      }
    },
    limits: {
      maxFileSize: '50MB',
      supportedFormats: ['JSON'],
      outputFormat: 'PDF'
    },
    requiredFields: {
      'logo-sc': 'string (base64)',
      'filtros': 'object',
      'dadosRelatorio': 'array',
      'dadosEmissao': 'object'
    },
    timestamp: formatDateTime(new Date())
  });
});

/**
 * Endpoint de teste com dados mock
 * GET /api/pdf/test
 */
router.get('/test', async (req, res, next) => {
  try {
    // Carrega dados mock do arquivo JSON
    const fs = require('fs');
    const path = require('path');
    
    const mockDataPath = path.join(process.cwd(), 'dados-json.json');
    
    if (!fs.existsSync(mockDataPath)) {
      return res.status(404).json({
        error: 'Arquivo de dados mock não encontrado',
        message: 'O arquivo dados-json.json não foi encontrado na raiz do projeto'
      });
    }

    const mockData = JSON.parse(fs.readFileSync(mockDataPath, 'utf8'));
    
    // Adiciona dados de emissão se não existirem
    if (!mockData.dadosEmissao) {
      mockData.dadosEmissao = {
        'Sistema': 'API PDF Generator',
        'Usuário': 'Teste',
        'Versão': require('../../package.json').version
      };
    }

    // Gera o PDF com dados mock
    const pdfService = new PDFService();
    const pdfBuffer = await pdfService.generatePDF(mockData);

    const fileName = `relatorio-teste-${Date.now()}.pdf`;

    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="${fileName}"`,
      'Content-Length': pdfBuffer.length
    });

    res.send(pdfBuffer);

  } catch (error) {
    next(error);
  }
});

module.exports = router;
