const { validateInputData, createValidationError } = require('../utils/validator');

/**
 * Middleware para validação de requests de geração de PDF
 */
const validatePDFRequest = (req, res, next) => {
  try {
    // Verifica se o body existe
    if (!req.body || Object.keys(req.body).length === 0) {
      throw createValidationError('Body da requisição está vazio', [
        'É necessário enviar dados JSON no corpo da requisição'
      ]);
    }

    // Verifica o Content-Type
    if (!req.is('application/json')) {
      throw createValidationError('Content-Type inválido', [
        'Content-Type deve ser application/json'
      ]);
    }

    // Valida a estrutura dos dados
    const validation = validateInputData(req.body);
    if (!validation.isValid) {
      throw createValidationError('Dados de entrada inválidos', validation.errors);
    }

    // Adiciona informações de validação ao request
    req.validationInfo = {
      recordCount: req.body.dadosRelatorio?.length || 0,
      hasLogo: !!req.body['logo-sc'],
      filtersCount: Object.keys(req.body.filtros || {}).length,
      hasEmissionData: !!req.body.dadosEmissao
    };

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Middleware para logging de requests
 */
const logRequest = (req, res, next) => {
  const startTime = Date.now();
  
  // Log da requisição
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.originalUrl}`);
  console.log(`User-Agent: ${req.get('User-Agent') || 'N/A'}`);
  console.log(`Content-Length: ${req.get('Content-Length') || 'N/A'}`);
  
  if (req.validationInfo) {
    console.log(`Records to process: ${req.validationInfo.recordCount}`);
    console.log(`Has logo: ${req.validationInfo.hasLogo}`);
    console.log(`Filters count: ${req.validationInfo.filtersCount}`);
  }

  // Intercepta o response para log de saída
  const originalSend = res.send;
  res.send = function(data) {
    const endTime = Date.now();
    const processingTime = endTime - startTime;
    
    console.log(`[${new Date().toISOString()}] Response: ${res.statusCode}`);
    console.log(`Processing time: ${processingTime}ms`);
    
    if (res.statusCode === 200 && data instanceof Buffer) {
      console.log(`PDF size: ${(data.length / 1024).toFixed(2)} KB`);
    }
    
    console.log('---');
    
    originalSend.call(this, data);
  };

  next();
};

/**
 * Middleware para rate limiting simples
 */
const rateLimiter = (() => {
  const requests = new Map();
  const WINDOW_SIZE = 60 * 1000; // 1 minuto
  const MAX_REQUESTS = 10; // máximo 10 requests por minuto por IP

  return (req, res, next) => {
    const clientIP = req.ip || req.connection.remoteAddress;
    const now = Date.now();
    
    // Limpa requests antigos
    if (requests.has(clientIP)) {
      const clientRequests = requests.get(clientIP);
      const validRequests = clientRequests.filter(time => now - time < WINDOW_SIZE);
      requests.set(clientIP, validRequests);
    }

    // Verifica limite
    const clientRequests = requests.get(clientIP) || [];
    if (clientRequests.length >= MAX_REQUESTS) {
      return res.status(429).json({
        error: 'Rate limit excedido',
        message: `Máximo de ${MAX_REQUESTS} requisições por minuto`,
        retryAfter: Math.ceil(WINDOW_SIZE / 1000)
      });
    }

    // Adiciona nova requisição
    clientRequests.push(now);
    requests.set(clientIP, clientRequests);

    next();
  };
})();

/**
 * Middleware para validação de tamanho de payload
 */
const validatePayloadSize = (req, res, next) => {
  const contentLength = parseInt(req.get('Content-Length') || '0');
  const maxSize = 50 * 1024 * 1024; // 50MB

  if (contentLength > maxSize) {
    const error = new Error('Payload muito grande');
    error.status = 413;
    return next(error);
  }

  next();
};

module.exports = {
  validatePDFRequest,
  logRequest,
  rateLimiter,
  validatePayloadSize
};
