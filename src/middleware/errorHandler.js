const errorHandler = (err, req, res, next) => {
  console.error('Error:', err);

  // Erro de JSON malformado
  if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
    return res.status(400).json({
      error: 'JSON inválido',
      message: 'O corpo da requisição contém JSON malformado'
    });
  }

  // Erro de payload muito grande
  if (err.code === 'LIMIT_FILE_SIZE' || err.type === 'entity.too.large') {
    return res.status(413).json({
      error: 'Payload muito grande',
      message: 'O arquivo enviado excede o limite permitido'
    });
  }

  // Erro de validação personalizado
  if (err.name === 'ValidationError') {
    return res.status(400).json({
      error: 'Erro de validação',
      message: err.message,
      details: err.details || null
    });
  }

  // Erro de PDF
  if (err.name === 'PDFError') {
    return res.status(500).json({
      error: 'Erro na geração do PDF',
      message: err.message
    });
  }

  // Erro genérico do servidor
  res.status(500).json({
    error: 'Erro interno do servidor',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Algo deu errado'
  });
};

module.exports = errorHandler;
