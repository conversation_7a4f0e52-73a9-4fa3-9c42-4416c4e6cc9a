const PDFDocument = require('pdfkit');
const { formatCurrency, calculateIndentation, formatDateTime, processReportData } = require('../utils/formatters');
const config = require('../config/app');

class PDFService {
  constructor() {
    this.doc = null;
    this.currentY = 0;
    this.pageHeight = 0;
    this.pageWidth = 0;
    this.margins = config.pdf.margins;
    this.filtros = null;
    this.dadosEmissao = null;
    this.logoBase64 = null;
  }

  /**
   * Gera o PDF completo
   * @param {Object} data - Dados para geração do PDF
   * @returns {Promise<Buffer>} Buffer do PDF gerado
   */
  async generatePDF(data) {
    try {
      this.doc = new PDFDocument({
        size: config.pdf.pageSize,
        layout: config.pdf.layout,
        margins: this.margins
      });

      this.pageHeight = this.doc.page.height;
      this.pageWidth = this.doc.page.width;
      this.filtros = data.filtros;
      this.dadosEmissao = data.dadosEmissao;
      this.logoBase64 = data['logo-sc'];

      // Processa os dados do relatório
      const dadosProcessados = processReportData(data.dadosRelatorio);

      // Gera o cabeçalho da primeira página
      await this.generateHeader(data['logo-sc'], true);

      // Gera a tabela de dados
      await this.generateTable(dadosProcessados);

      // Finaliza o documento
      this.doc.end();

      // Retorna o buffer do PDF
      return new Promise((resolve, reject) => {
        const buffers = [];
        this.doc.on('data', buffers.push.bind(buffers));
        this.doc.on('end', () => {
          const pdfBuffer = Buffer.concat(buffers);
          resolve(pdfBuffer);
        });
        this.doc.on('error', reject);
      });

    } catch (error) {
      const pdfError = new Error(`Erro na geração do PDF: ${error.message}`);
      pdfError.name = 'PDFError';
      throw pdfError;
    }
  }

  /**
   * Gera o cabeçalho do documento
   * @param {string} logoBase64 - Logo em base64
   * @param {boolean} isFirstPage - Se é a primeira página
   */
  async generateHeader(logoBase64, isFirstPage = false) {
    const headerHeight = 80;

    // Adiciona o logo se disponível
    if (logoBase64 && typeof logoBase64 === 'string') {
      try {
        const logoBuffer = Buffer.from(logoBase64.replace(/^data:image\/[a-z]+;base64,/, ''), 'base64');
        this.doc.image(logoBuffer, this.margins.left, this.margins.top, {
          width: 60,
          height: 60
        });
      } catch (error) {
        console.warn('Erro ao processar logo:', error.message);
        // Desenha um placeholder se o logo falhar
        this.doc.rect(this.margins.left, this.margins.top, 60, 60)
               .stroke();
        this.doc.fontSize(8)
               .text('LOGO', this.margins.left + 20, this.margins.top + 25);
      }
    } else {
      // Desenha um placeholder se não houver logo
      this.doc.rect(this.margins.left, this.margins.top, 60, 60)
             .stroke();
      this.doc.fontSize(8)
             .text('LOGO', this.margins.left + 20, this.margins.top + 25);
    }

    // Título principal
    this.doc.font(config.pdf.fonts.bold)
           .fontSize(16)
           .text('RELATÓRIO DETALHADO DE GASTOS', this.margins.left + 80, this.margins.top + 10);

    // Subtítulo
    this.doc.font(config.pdf.fonts.regular)
           .fontSize(12)
           .text('Estado de Santa Catarina', this.margins.left + 80, this.margins.top + 35);

    this.currentY = this.margins.top + headerHeight;

    // Adiciona filtros apenas na primeira página
    if (isFirstPage && this.filtros) {
      this.generateFilters();
    }

    // Adiciona linha separadora
    this.doc.moveTo(this.margins.left, this.currentY)
           .lineTo(this.pageWidth - this.margins.right, this.currentY)
           .stroke();
    
    this.currentY += 10;
  }

  /**
   * Gera a seção de filtros
   */
  generateFilters() {
    this.currentY += 20;
    
    this.doc.font(config.pdf.fonts.bold)
           .fontSize(12)
           .text('Filtros Aplicados:', this.margins.left, this.currentY);
    
    this.currentY += 20;

    // Exibe cada filtro
    Object.entries(this.filtros).forEach(([key, value]) => {
      this.doc.font(config.pdf.fonts.bold)
             .fontSize(10)
             .text(`${key}:`, this.margins.left + 20, this.currentY);
      
      this.doc.font(config.pdf.fonts.regular)
             .text(value, this.margins.left + 120, this.currentY);
      
      this.currentY += 15;
    });

    this.currentY += 10;
  }

  /**
   * Gera o cabeçalho da tabela
   */
  generateTableHeader() {
    const headerHeight = 25;
    // Larguras otimizadas para A4 PAISAGEM (largura útil ~742px)
    // Agora temos muito mais espaço horizontal
    const colWidths = [350, 98, 98, 98, 98]; // Total: 742px
    const headers = ['Detalhamento', 'Custo', 'Despesa', 'Investimento', 'Total'];

    let currentX = this.margins.left;

    // Fundo do cabeçalho
    this.doc.rect(this.margins.left, this.currentY,
                  this.pageWidth - this.margins.left - this.margins.right, headerHeight)
           .fill('#f0f0f0');

    // Texto do cabeçalho
    this.doc.fill('#000000')
           .font(config.pdf.fonts.bold)
           .fontSize(10);

    headers.forEach((header, index) => {
      this.doc.text(header, currentX + 5, this.currentY + 8, {
        width: colWidths[index] - 10,
        align: index === 0 ? 'left' : 'center'
      });
      currentX += colWidths[index];
    });

    this.currentY += headerHeight;
  }

  /**
   * Verifica se precisa de nova página
   * @param {number} requiredHeight - Altura necessária
   */
  checkNewPage(requiredHeight = 30) {
    const footerSpace = 80; // Espaço reservado para o rodapé
    if (this.currentY + requiredHeight > this.pageHeight - this.margins.bottom - footerSpace) {
      this.doc.addPage();
      this.generateHeader(this.logoBase64, false); // Cabeçalho sem filtros
      this.generateTableHeader();
    }
  }

  /**
   * Gera uma linha da tabela
   * @param {Object} item - Item de dados
   */
  generateTableRow(item) {
    const colWidths = [350, 98, 98, 98, 98]; // Mesmas larguras do cabeçalho
    const indentation = calculateIndentation(item.nivel);

    // Calcula altura necessária baseada no texto do detalhamento
    const textWidth = colWidths[0] - indentation - 10;
    const textHeight = this.doc.heightOfString(item.nome, {
      width: textWidth,
      fontSize: 9
    });
    const rowHeight = Math.max(20, textHeight + 10); // Altura mínima de 20px

    this.checkNewPage(rowHeight);

    let currentX = this.margins.left;
    const startY = this.currentY;

    // Detalhamento com indentação e quebra de linha
    this.doc.font(config.pdf.fonts.regular)
           .fontSize(9)
           .text(item.nome, currentX + indentation, startY + 5, {
             width: textWidth,
             align: 'left'
           });
    currentX += colWidths[0];

    // Valores monetários - centralizados verticalmente na linha
    // Usando fonte menor para garantir que não quebrem
    const values = [
      formatCurrency(item.valor_custo),
      formatCurrency(item.valor_despesa),
      formatCurrency(item.valor_investimento),
      formatCurrency(item.totalfinanceiro)
    ];

    const textYPosition = startY + (rowHeight / 2) - 5; // Centraliza verticalmente

    this.doc.fontSize(9); // Fonte normal para valores monetários (temos mais espaço)
    values.forEach((value, index) => {
      this.doc.text(value, currentX + 5, textYPosition, {
        width: colWidths[index + 1] - 10,
        align: 'right',
        lineBreak: false // Força não quebrar linha
      });
      currentX += colWidths[index + 1];
    });

    // Desenha linhas de separação entre as colunas (opcional)
    this.drawRowSeparators(startY, rowHeight, colWidths);

    this.currentY += rowHeight;
  }

  /**
   * Desenha linhas de separação entre colunas (opcional)
   * @param {number} startY - Posição Y inicial da linha
   * @param {number} rowHeight - Altura da linha
   * @param {Array} colWidths - Larguras das colunas
   */
  drawRowSeparators(startY, rowHeight, colWidths) {
    // Linha horizontal inferior mais sutil
    this.doc.moveTo(this.margins.left, startY + rowHeight)
           .lineTo(this.margins.left + colWidths.reduce((a, b) => a + b, 0), startY + rowHeight)
           .stroke('#e0e0e0');

    // Linhas verticais entre colunas mais sutis
    let currentX = this.margins.left;
    colWidths.forEach((width, index) => {
      if (index < colWidths.length - 1) { // Não desenha após a última coluna
        currentX += width;
        this.doc.moveTo(currentX, startY)
               .lineTo(currentX, startY + rowHeight)
               .stroke('#e0e0e0');
      }
    });
  }

  /**
   * Gera a tabela completa
   * @param {Array} dados - Dados processados
   */
  async generateTable(dados) {
    this.generateTableHeader();

    dados.forEach(item => {
      this.generateTableRow(item);
    });

    // Adiciona rodapé na última página
    this.generateFooter();
  }

  /**
   * Gera o rodapé do documento
   */
  generateFooter() {
    const footerY = this.pageHeight - this.margins.bottom;
    
    // Linha separadora
    this.doc.moveTo(this.margins.left, footerY - 30)
           .lineTo(this.pageWidth - this.margins.right, footerY - 30)
           .stroke();

    // Dados de emissão
    if (this.dadosEmissao) {
      this.doc.font(config.pdf.fonts.regular)
             .fontSize(8);

      Object.entries(this.dadosEmissao).forEach(([key, value], index) => {
        const x = this.margins.left + (index * 150);
        this.doc.text(`${key}: ${value}`, x, footerY - 20);
      });
    }

    // Data e hora de geração
    const now = new Date();
    this.doc.text(`Gerado em: ${formatDateTime(now)}`, 
                  this.pageWidth - this.margins.right - 150, footerY - 20);
  }
}

module.exports = PDFService;
