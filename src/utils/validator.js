/**
 * Valida se uma string é base64 válida
 * @param {string} str - String a ser validada
 * @returns {boolean} True se for base64 válido
 */
function isValidBase64(str) {
  if (!str || typeof str !== 'string') return false;
  
  try {
    // Remove o prefixo data:image se existir
    const base64Data = str.replace(/^data:image\/[a-z]+;base64,/, '');
    
    // Verifica se é base64 válido
    return btoa(atob(base64Data)) === base64Data;
  } catch (err) {
    return false;
  }
}

/**
 * Valida a estrutura do JSON de entrada
 * @param {Object} data - Dados a serem validados
 * @returns {Object} Resultado da validação
 */
function validateInputData(data) {
  const errors = [];
  
  // Verifica se data é um objeto
  if (!data || typeof data !== 'object') {
    errors.push('Os dados devem ser um objeto JSON válido');
    return { isValid: false, errors };
  }
  
  // Valida logo-sc
  if (!data['logo-sc']) {
    errors.push('Campo "logo-sc" é obrigatório');
  } else if (!isValidBase64(data['logo-sc'])) {
    errors.push('Campo "logo-sc" deve conter uma imagem em base64 válida');
  }
  
  // Valida filtros
  if (!data.filtros || typeof data.filtros !== 'object') {
    errors.push('Campo "filtros" é obrigatório e deve ser um objeto');
  } else {
    const requiredFilters = ['Entidade Custos', 'Período', 'Tipo de Gasto'];
    requiredFilters.forEach(filter => {
      if (!data.filtros[filter]) {
        errors.push(`Filtro "${filter}" é obrigatório`);
      }
    });
  }
  
  // Valida dadosRelatorio
  if (!data.dadosRelatorio || !Array.isArray(data.dadosRelatorio)) {
    errors.push('Campo "dadosRelatorio" é obrigatório e deve ser um array');
  } else if (data.dadosRelatorio.length === 0) {
    errors.push('Campo "dadosRelatorio" não pode estar vazio');
  } else {
    // Valida estrutura dos itens do relatório
    data.dadosRelatorio.forEach((item, index) => {
      const requiredFields = ['nivel', 'nome', 'valor_custo', 'valor_despesa', 'valor_investimento', 'totalfinanceiro'];
      requiredFields.forEach(field => {
        if (item[field] === undefined || item[field] === null) {
          errors.push(`Item ${index}: campo "${field}" é obrigatório`);
        }
      });
      
      // Valida se nivel é um número
      if (item.nivel !== undefined && isNaN(parseInt(item.nivel))) {
        errors.push(`Item ${index}: campo "nivel" deve ser um número`);
      }
    });
  }
  
  // Valida dadosEmissao
  if (!data.dadosEmissao || typeof data.dadosEmissao !== 'object') {
    errors.push('Campo "dadosEmissao" é obrigatório e deve ser um objeto');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Cria um erro de validação personalizado
 * @param {string} message - Mensagem de erro
 * @param {Array} details - Detalhes dos erros
 * @returns {Error} Erro de validação
 */
function createValidationError(message, details = []) {
  const error = new Error(message);
  error.name = 'ValidationError';
  error.details = details;
  return error;
}

module.exports = {
  isValidBase64,
  validateInputData,
  createValidationError
};
