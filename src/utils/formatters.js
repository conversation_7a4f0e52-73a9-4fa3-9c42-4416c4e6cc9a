/**
 * Formata um valor numérico como moeda brasileira
 * @param {number|string} value - Valor a ser formatado
 * @returns {string} Valor formatado como moeda
 */
function formatCurrency(value) {
  if (value === null || value === undefined || value === '') {
    return 'R$ 0,00';
  }
  
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(numValue)) {
    return 'R$ 0,00';
  }
  
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(numValue);
}

/**
 * Calcula o deslocamento horizontal baseado no nível hierárquico
 * @param {number} nivel - Nível hierárquico (0, 1, 2, 3, 4...)
 * @returns {number} Deslocamento em pixels
 */
function calculateIndentation(nivel) {
  const baseIndent = 10; // Indentação base
  const indentStep = 15; // Incremento por nível
  return baseIndent + (nivel * indentStep);
}

/**
 * Trunca texto se exceder o limite de caracteres
 * @param {string} text - Texto a ser truncado
 * @param {number} maxLength - Comprimento máximo
 * @returns {string} Texto truncado
 */
function truncateText(text, maxLength = 50) {
  if (!text || text.length <= maxLength) {
    return text || '';
  }
  return text.substring(0, maxLength - 3) + '...';
}

/**
 * Formata data no padrão brasileiro
 * @param {Date|string} date - Data a ser formatada
 * @returns {string} Data formatada
 */
function formatDate(date) {
  if (!date) return '';
  
  const dateObj = date instanceof Date ? date : new Date(date);
  
  return dateObj.toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
}

/**
 * Formata data e hora no padrão brasileiro
 * @param {Date|string} date - Data a ser formatada
 * @returns {string} Data e hora formatadas
 */
function formatDateTime(date) {
  if (!date) return '';
  
  const dateObj = date instanceof Date ? date : new Date(date);
  
  return dateObj.toLocaleString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * Processa os dados do relatório para garantir tipos corretos
 * @param {Array} dadosRelatorio - Array de dados do relatório
 * @returns {Array} Dados processados
 */
function processReportData(dadosRelatorio) {
  if (!Array.isArray(dadosRelatorio)) {
    throw new Error('dadosRelatorio deve ser um array');
  }
  
  return dadosRelatorio.map(item => ({
    ...item,
    nivel: parseInt(item.nivel) || 0,
    valor_custo: parseFloat(item.valor_custo) || 0,
    valor_despesa: parseFloat(item.valor_despesa) || 0,
    valor_investimento: parseFloat(item.valor_investimento) || 0,
    totalfinanceiro: parseFloat(item.totalfinanceiro) || 0,
    nome: item.nome || '',
    sigla: item.sigla || '',
    codigo: item.codigo || 0
  }));
}

module.exports = {
  formatCurrency,
  calculateIndentation,
  truncateText,
  formatDate,
  formatDateTime,
  processReportData
};
