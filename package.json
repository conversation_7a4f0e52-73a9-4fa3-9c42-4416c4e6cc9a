{"name": "pdf-generator-api", "version": "1.0.0", "description": "API Node.js para geração de PDFs de relatórios detalhados de gastos", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["pdf", "api", "nodejs", "relatorio", "gastos"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^17.1.0", "express": "^5.1.0", "helmet": "^8.1.0", "morgan": "^1.10.0", "multer": "^2.0.1", "node-fetch": "^2.7.0", "pdfkit": "^0.17.1"}, "devDependencies": {"nodemon": "^3.1.10"}}